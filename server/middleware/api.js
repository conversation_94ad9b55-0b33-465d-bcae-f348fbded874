const request = require('request');
const { SERVER_API_HOST_URL } = require('../../config');

const urlReg = /^\/(v1|v2|v3|rest|query|agent|web2|Web2|assets|Agent)\//;

console.log('API middleware loaded');

module.exports = async (ctx, next) => {
    console.log('API middleware called for:', ctx.url);
    if (urlReg.test(ctx.url) || /.ajax$/.test(ctx.path)) {
        console.log('API middleware handling:', ctx.url);
        console.log('Proxying to:', SERVER_API_HOST_URL + ctx.url);
        const options = {
            uri: ctx.url,
            baseUrl: SERVER_API_HOST_URL,
            gzip: true,
            json: true,
        };

        if (
            ctx.url.startsWith('/v1/agentwebserv/batchorder/download') ||
            ctx.url.startsWith('/v1/agentwebserv/agent/bookings/export') ||
            ctx.url.startsWith('/v1/agentwebserv/agentbatch/bookings/export')
        ) {
            options.encoding = null;
        }
        await new Promise(resolve => {
            ctx.req.pipe(
                request(options, (error, response, body) => {
                    if (error) {
                        throw error;
                    }
                    // 复制响应头
                    if (response.headers) {
                        Object.keys(response.headers).forEach(key => {
                            if (key.toLowerCase() !== 'transfer-encoding') {
                                ctx.set(key, response.headers[key]);
                            }
                        });
                    }
                    ctx.body = body;
                    ctx.status = response.statusCode || 200;
                    resolve();
                }),
            );
        });
    } else {
        await next();
    }
};
